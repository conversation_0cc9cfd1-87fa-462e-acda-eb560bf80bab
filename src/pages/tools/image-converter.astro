---
import Layout from '../../layouts/Layout.astro';
import ImageConverterComponent from '../../components/converters/ImageConverter.tsx';
import '../../styles/global.css';

const title = 'Universal Image Converter - Convert Any Image Format';
const description = 'Convert between PNG, JPG, WebP, GIF, BMP, ICO, TIFF, AVIF and more. Free online image converter with batch processing.';
---

<Layout title={title} description={description} toolType="image">
  <main>
    <ImageConverterComponent client:load />
  </main>
</Layout>