---
import Layout from '../../layouts/Layout.astro';
import ImageConverter from '../../components/converters/ImageConverter.tsx';
import '../../styles/global.css';

// Pre-render all dynamic routes at build time for SEO
export const prerender = true;

export async function getStaticPaths() {
  // All supported image formats
  // Note: HEIC is supported via libheif-js, others via image-rs
  const formats = [
    { key: 'png', name: 'PNG' },
    { key: 'jpg', name: 'JP<PERSON>' },
    { key: 'webp', name: 'WebP' },
    { key: 'gif', name: 'GIF' },
    { key: 'bmp', name: 'B<PERSON>' },
    { key: 'ico', name: 'I<PERSON>' },
    { key: 'tiff', name: 'TIFF' },
    { key: 'avif', name: 'AVI<PERSON>' },
    { key: 'heic', name: 'HEIC' }
  ];

  // Formats that support quality adjustment (useful for same-format conversion)
  const qualityFormats = ['jpg', 'webp', 'avif'];

  // Generate all possible combinations
  const paths = [];
  
  for (const from of formats) {
    for (const to of formats) {
      // Skip same format conversions EXCEPT for quality-adjustable formats
      if (from.key === to.key && !qualityFormats.includes(from.key)) continue;
      
      paths.push({
        params: { from: from.key, to: to.key }
      });
    }
  }
  
  return paths;
}

// Dynamic route parameters
const fromKey = Astro.params.from;
const toKey = Astro.params.to;
// Human-friendly format names
const formatNameMap: Record<string, string> = {
  png: 'PNG', jpg: 'JPEG/JPG', webp: 'WebP', gif: 'GIF',
  bmp: 'BMP', ico: 'ICO', tiff: 'TIFF', avif: 'AVIF', heic: 'HEIC'
};
const fromFormat = formatNameMap[fromKey as string] || fromKey?.toUpperCase();
const toFormat = formatNameMap[toKey as string] || toKey?.toUpperCase();
// Title and description logic
const isSame = fromKey === toKey;
const title = isSame
  ? `${fromFormat} Compressor - Optimize & Resize ${fromFormat} Images`
  : `${fromFormat} to ${toFormat} Converter - Free Online Tool`;
const description = isSame
  ? `Compress and optimize ${fromFormat} images by adjusting quality. Reduce file size while maintaining visual quality. No upload required, 100% private.`
  : `Convert ${fromFormat} images to ${toFormat} format instantly in your browser. No upload required, 100% private and secure. Supports batch conversion.`;

// Schema.org structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": title,
  "description": description,
  "url": `https://formatfuse.com/convert/${fromKey}-to-${toKey}`,
  "applicationCategory": "UtilitiesApplication",
  "operatingSystem": "Any",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "featureList": [
    "Browser-based conversion",
    "No file uploads",
    "Batch processing",
    "Privacy-focused",
    "No watermarks",
    "Unlimited conversions"
  ]
};
---

<Layout title={title} description={description} sourceFormat={fromKey} targetFormat={toKey} toolType="image">
  <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
  
  <main>
    <ImageConverter sourceFormat={fromKey} targetFormat={toKey} client:load />
  </main>
</Layout>