---
import { <PERSON>, Sparkles } from 'lucide-react';

const currentYear = new Date().getFullYear();

const footerTools = {
  pdf: [
    { name: 'Merge PDF', href: '/convert/pdf-merge' },
    { name: 'Split PDF', href: '/convert/pdf-split' },
    { name: 'Compress PDF', href: '/convert/pdf-compress' },
    { name: 'JPG to PDF', href: '/convert/jpg-to-pdf' },
  ],
  image: [
    { name: 'Image Converter', href: '/tools/image-converter' },
    { name: 'Image Compressor', href: '/tools/image-compressor' },
    { name: 'Image Resizer', href: '/tools/image-resizer' },
    { name: 'PNG to JPG', href: '/convert/png-to-jpg' },
  ],
  developer: [
    { name: 'JSON Formatter', href: '/tools/json-formatter' },
    { name: 'Base64 Encoder', href: '/tools/base64-encoder' },
    { name: 'QR Generator', href: '/tools/qr-generator' },
    { name: 'UUID Generator', href: '/tools/uuid-generator' },
  ],
  archive: [
    { name: 'Extract ZIP', href: '/tools/zip-extract' },
    { name: 'Create ZIP', href: '/tools/create-zip' },
    { name: 'Extract 7Z', href: '/tools/7z-extract' },
    { name: 'Extract RAR', href: '/tools/rar-extract' },
  ],
};

const features = [
  { icon: Shield, text: 'Privacy-First Conversion' },
  { icon: Sparkles, text: 'No Sign-up Required' },
];
---

<footer class="relative overflow-hidden">
  <div class="absolute inset-0 bg-gradient-to-b from-transparent via-primary/[0.02] to-primary/[0.05]" />
  
  <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    {/* Main footer content */}
    <div class="py-12 border-t border-border/50">
      <div class="flex flex-col lg:flex-row gap-12 lg:gap-16">
        {/* Brand section */}
        <div class="lg:w-1/3 lg:pr-8">
          <h3 class="font-bold text-2xl mb-4">FormatFuse</h3>
          <p class="text-sm text-muted-foreground mb-6 leading-relaxed">
            Professional file conversion tools that work instantly in your browser. 
            No uploads, no servers, no limits. Everything stays on your device.
          </p>
          
          {/* Key features */}
          <div class="space-y-3">
            {features.map((feature) => {
              const Icon = feature.icon;
              return (
                <div class="flex items-center gap-3">
                  <Icon className="w-4 h-4 text-primary" />
                  <span class="text-sm text-muted-foreground">{feature.text}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Tools Grid - Right Aligned */}
        <div class="flex-1">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-8 lg:gap-12 lg:justify-items-end">
            {/* PDF Tools */}
            <div>
              <h4 class="font-semibold mb-4 text-sm">PDF Tools</h4>
              <ul class="space-y-2">
                {footerTools.pdf.map((link) => (
                  <li>
                    <a 
                      href={link.href} 
                      class="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Image Tools */}
            <div>
              <h4 class="font-semibold mb-4 text-sm">Image Tools</h4>
              <ul class="space-y-2">
                {footerTools.image.map((link) => (
                  <li>
                    <a 
                      href={link.href} 
                      class="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Developer Tools */}
            <div>
              <h4 class="font-semibold mb-4 text-sm">Developer Tools</h4>
              <ul class="space-y-2">
                {footerTools.developer.map((link) => (
                  <li>
                    <a 
                      href={link.href} 
                      class="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Archive Tools */}
            <div>
              <h4 class="font-semibold mb-4 text-sm">Archive Tools</h4>
              <ul class="space-y-2">
                {footerTools.archive.map((link) => (
                  <li>
                    <a 
                      href={link.href} 
                      class="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Bottom section */}
    <div class="py-6 border-t border-border/50">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <div class="text-sm text-muted-foreground text-center md:text-left">
          © {currentYear} FormatFuse • A <a 
            href="https://raylabs.io" 
            target="_blank" 
            rel="noopener noreferrer" 
            class="hover:text-primary transition-colors"
          >RayLabs</a> Company
        </div>
        
        <div class="flex items-center gap-6">
          <a href="https://raylabs.io" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary transition-colors">
            About
          </a>
          <a href="/privacy" class="text-sm text-muted-foreground hover:text-primary transition-colors">
            Privacy
          </a>
          <a href="/terms" class="text-sm text-muted-foreground hover:text-primary transition-colors">
            Terms
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>