---
import '../styles/global.css';
import Navigation from '../components/Navigation.tsx';
import Footer from '../components/layout/Footer.astro';
import WasmPrefetch from '../components/WasmPrefetch.astro';
import ToasterProvider from '../components/ToasterProvider.tsx';
import { getImagePath } from 'astro-opengraph-images';

export interface Props {
  title: string;
  description?: string;
  sourceFormat?: string;
  targetFormat?: string;
  keywords?: string;
  ogImage?: string;
  toolType?: string;
}

const { 
  title, 
  description = "Free online file converters - PDF, Image, Document. No uploads, 100% private.",
  sourceFormat,
  targetFormat,
  keywords = "file converter, online converter, pdf converter, image converter, free converter, privacy-first, no upload",
  ogImage,
  toolType
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);

// Generate OG image path
const generatedOgImageUrl = getImagePath({
  url: Astro.url,
  site: Astro.site
});

const finalOgImage = ogImage || generatedOgImageUrl;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title} - FormatFuse</title>
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Sitemap -->
    <link rel="sitemap" href="/sitemap-index.xml" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Open Graph -->
    <meta property="og:title" content={`${title} - FormatFuse`} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:image" content={new URL(finalOgImage, Astro.site)} />
    <meta property="og:site_name" content="FormatFuse" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={`${title} - FormatFuse`} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={new URL(finalOgImage, Astro.site)} />
    
    <!-- Additional SEO -->
    <meta name="robots" content="index, follow" />
    <meta name="author" content="FormatFuse" />
    
    <!-- Theme detection -->
    <script is:inline>
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();
      
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    </script>
    
    <!-- WASM Prefetch hints -->
    <WasmPrefetch sourceFormat={sourceFormat} targetFormat={targetFormat} />
    
    <!-- Google Tag Manager -->
    <script is:inline>
      // Check if GTM is already initialized
      if (!window.dataLayer || !window.google_tag_manager) {
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
          
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });

        var firstScript = document.getElementsByTagName('script')[0];
        var gtmScript = document.createElement('script');
        gtmScript.async = true;
        gtmScript.src = 'https://www.googletagmanager.com/gtm.js?id=GTM-MJKP526Z';
        firstScript.parentNode.insertBefore(gtmScript, firstScript);
      }
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body class="min-h-screen antialiased flex flex-col">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MJKP526Z"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <Navigation client:load />
    <div class="flex-1 flex flex-col min-h-0">
      <slot />
    </div>
    <Footer />
    <ToasterProvider client:only="react" />
  </body>
</html>