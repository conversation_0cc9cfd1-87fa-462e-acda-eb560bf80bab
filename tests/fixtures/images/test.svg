<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="200" height="200" fill="#f0f0f0"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect width="20" height="20" fill="none" stroke="#ccc" stroke-width="0.5"/>
    </pattern>
  </defs>
  <rect width="200" height="200" fill="url(#grid)"/>
  
  <!-- Shapes to test SVG rendering -->
  <circle cx="50" cy="50" r="30" fill="#ff0000" opacity="0.8"/>
  <rect x="120" y="20" width="60" height="60" fill="#00ff00" rx="5"/>
  <polygon points="100,120 140,190 60,190" fill="#0000ff"/>
  
  <!-- Text -->
  <text x="100" y="110" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">
    SVG Test
  </text>
  
  <!-- Path -->
  <path d="M 10,150 Q 50,100 90,150 T 170,150" stroke="#ff00ff" stroke-width="3" fill="none"/>
</svg>